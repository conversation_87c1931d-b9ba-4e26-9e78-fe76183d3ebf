import asyncio

from autogen_agentchat.agents import <PERSON><PERSON><PERSON>
from autogen_agentchat.teams import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Graph<PERSON><PERSON>
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Create an OpenAI model client
client = OpenAIChatCompletionClient(
    model="qwen-plus",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    api_key="sk-f95b28efff5f434db7a3be957504b586",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": False
    }
)

async def main():

    # Create the writer agent
    writer = AssistantAgent("writer", model_client=client, system_message="Draft a short paragraph on climate change.")

    # Create the reviewer agent
    reviewer = AssistantAgent("reviewer", model_client=client, system_message="Review the draft and suggest improvements.")

    # Build the graph
    builder = DiGraphBuilder()
    builder.add_node(writer).add_node(reviewer)
    builder.add_edge(writer, reviewer)

    # Build and validate the graph
    graph = builder.build()

    # Create the flow
    flow = GraphFlow([writer, reviewer], graph=graph)

    stream = flow.run_stream(task="Write a short paragraph about climate change.")
    async for event in stream:  # type: ignore
        print(event)

if __name__ == "__main__":
    asyncio.run(main())