#!/bin/bash

# 脚本默认配置
remote_hosts=()
instance_count=1
spec=""
CORES_PER_INSTANCE=16
MEMORY_SIZE="32g"
disk_devices=()
data_mount_points=()

# 显示帮助信息
show_help() {
    cat << EOF
Elasticsearch 多实例测试脚本

用法: $0 [选项]

选项:
    -n, --instances NUM     设置实例数量 (默认: 1)
    -i, --ips "IP1,IP2,IP3" 设置IP地址列表，用逗号分隔
                           例如: "**********,**********,**********"
    -s, --spec SPEC        设置规格，格式为 XCYg (如: 16C32G)
                           X为CPU核心数，Y为内存大小(GB)
    -d, --disks "DEV1,DEV2" 设置磁盘设备列表，用逗号分隔
                           例如: "/dev/nvme0n1p1,/dev/nvme1n1p1,/dev/nvme2n1p1"
    -h, --help             显示此帮助信息

示例:
    $0 -n 3 -i "**********,**********,**********" -s "16C32G" -d "/dev/nvme0n1p1,/dev/nvme1n1p1"
    $0 --instances 2 --ips "**********,**********" --spec "8C16G" --disks "/dev/sda1,/dev/sdb1,/dev/sdc1"

说明:
    - 实例数: 控制for循环中的实例数量
    - IP列表: 会被解析为数组格式 remote_hosts=("IP1" "IP2" "IP3")
    - 规格: 16C32G 会设置 CORES_PER_INSTANCE=16 和内存为32g
    - 磁盘设备: 会自动创建对应数量的data目录并挂载到指定磁盘
EOF
}

# 解析规格参数
parse_spec() {
    local spec_input="$1"
    if [[ $spec_input =~ ^([0-9]+)C([0-9]+)G$ ]]; then
        CORES_PER_INSTANCE="${BASH_REMATCH[1]}"
        MEMORY_SIZE="${BASH_REMATCH[2]}g"
        echo "解析规格: CPU核心数=${CORES_PER_INSTANCE}, 内存=${MEMORY_SIZE}"
    else
        echo "错误: 规格格式不正确。请使用格式如 '16C32G'"
        exit 1
    fi
}

# 解析IP列表
parse_ips() {
    local ip_string="$1"
    IFS=',' read -ra remote_hosts <<< "$ip_string"
    echo "解析IP列表: ${remote_hosts[*]}"
}

# 解析磁盘设备列表
parse_disks() {
    local disk_string="$1"
    IFS=',' read -ra disk_devices <<< "$disk_string"
    echo "解析磁盘设备列表: ${disk_devices[*]}"

    # 生成挂载点数组
    data_mount_points=()
    for i in "${!disk_devices[@]}"; do
        mount_point="/data$((i+1))"
        data_mount_points+=("$mount_point")
    done
    echo "生成挂载点: ${data_mount_points[*]}"
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--instances)
            instance_count="$2"
            shift 2
            ;;
        -i|--ips)
            parse_ips "$2"
            shift 2
            ;;
        -s|--spec)
            parse_spec "$2"
            shift 2
            ;;
        -d|--disks)
            parse_disks "$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必要参数
if [[ ${#remote_hosts[@]} -eq 0 ]]; then
    echo "错误: 必须指定IP地址列表"
    show_help
    exit 1
fi

echo "配置信息:"
echo "  实例数量: $instance_count"
echo "  IP地址列表: ${remote_hosts[*]}"
echo "  CPU核心数: $CORES_PER_INSTANCE"
echo "  内存大小: $MEMORY_SIZE"
echo "  磁盘设备: ${disk_devices[*]}"
echo "  挂载点: ${data_mount_points[*]}"
echo ""

# 磁盘挂载函数
setup_disk_mounts() {
    echo "开始设置磁盘挂载..."

    for i in "${!disk_devices[@]}"; do
        local device="${disk_devices[$i]}"
        local mount_point="${data_mount_points[$i]}"

        echo "处理磁盘 $device -> $mount_point"

        # 创建挂载点目录
        mkdir -p "$mount_point"

        # 检查设备是否已挂载
        if mount | grep -q "$device"; then
            echo "  设备 $device 已挂载"
        else
            # 格式化磁盘（如果需要）
            echo "  格式化磁盘 $device..."
            mkfs.ext4 -F "$device" 2>/dev/null || echo "  警告: 磁盘格式化失败或已格式化"

            # 挂载磁盘
            echo "  挂载 $device 到 $mount_point..."
            mount "$device" "$mount_point" || echo "  警告: 挂载失败"

            # 设置权限
            chmod 755 "$mount_point"
        fi
    done

    echo "磁盘挂载设置完成"
}

# 生成数据路径函数
generate_data_paths() {
    local count=$1
    local instance=$2
    local path_type=$3  # "data" 或 "logs"

    local paths=()
    for mount_point in "${data_mount_points[@]}"; do
        paths+=("${mount_point}/es/paraller_test/paraller${count}/instance${instance}/${path_type}")
    done

    # 用逗号连接路径
    local result=$(IFS=','; echo "${paths[*]}")
    echo "$result"
}

# 当前日期
current_date=$(date +%Y%m%d)


# 注意: ip_get函数已被参数解析替代，remote_hosts现在通过命令行参数设置
# 保留此函数以兼容现有代码，但不再修改remote_hosts数组
ip_get(){
	cpu_vendor=$1
	echo "机器为 $cpu_vendor"
	echo "使用命令行指定的IP列表: ${remote_hosts[*]}"
}

#关闭集群es
shutdown_es_exec() {
    for host in "${remote_hosts[@]}"; do
        # 关闭ES
        echo "host = $host "
        echo "$remote_hosts"
        echo "正在关闭 $host ES"
        ssh "$host" "jps|grep lasticsearch |awk '{print $1}'|xargs kill -9 ;rm -rf /data1/*"
    done
    sleep 10
}

node_list=""

paraller_test_config_single_marchine(){
    count=$instance_count
    echo "count = $count"
    jps|grep lasticsearch |awk '{print $1}'|xargs kill -9
    # 设置磁盘挂载（如果配置了磁盘设备）
    if [[ ${#disk_devices[@]} -gt 0 ]]; then
        setup_disk_mounts
    fi

    echo "NODE_LIST = ${nodeListStr}"
    echo 3 > /proc/sys/vm/drop_caches
    declare -a nodeList
    declare -a portList
    declare -a node_http_port_list

    for ((k=1; k<=instance_count; k++)); do
                for node in "${remote_hosts[@]}"; do
                        node_http="${node}:$((9200 + k - 1))"
                        tcp_port="${node}:$((8200 + 10 * instance_count + k - 1))"
                        nodeList+=("node${k}_${node}")  # 区分不同节点
                        portList+=("${tcp_port}")
                        node_http_port_list+=("${node_http}")
		            done
    done
    nodeListStr=$(printf '"%s",' "${nodeList[@]}")
    nodeListStr="${nodeListStr%,}"
    echo "nodeListStr = ${nodeListStr}"
    portListStr=$(printf '"%s",' "${portList[@]}")
    portListStr="${portListStr%,}"
    echo "port_list =  ${portList}"
    node_http_port_list_str=$(printf '"%s",' "${node_http_port_list[@]}")
    node_http_port_list_str="${node_http_port_list_str%,}"
    echo "node_http = ${node_http_port_list_str}"
    node_list="${node_http_port_list_str%,}"

    for ((j=1; j<=count; j++)); do
        HTTP_PORT=$((19200 + j - 1))
        TCP_PORT=$((8200 + 10 * count + j - 1))

        CONFIG_DIR="/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$j"

        # 根据配置的磁盘数量动态生成数据和日志路径
        if [[ ${#data_mount_points[@]} -gt 0 ]]; then
            DATA_PATH=$(generate_data_paths "$count" "$j" "data")
            #LOGS_PATH=$(generate_data_paths "$count" "$j" "logs")
	    LOGS_PATH="/data1/es/paraller_test/paraller$count/instance${j}/logs"
        else
            # 如果没有配置磁盘，使用默认路径
            DATA_PATH="/data1/es/paraller_test/paraller$count/instance${j}/data"
            LOGS_PATH="/data1/es/paraller_test/paraller$count/instance${j}/logs"
        fi
        CONFIG_FILE="$CONFIG_DIR/elasticsearch.yml"
        CLUSTER_NAME="es_paraller_test_instance$count"

        echo "CLUSTER_NAME = $CLUSTER_NAME"
        echo "DATA_PATH = $DATA_PATH"
        echo "LOGS_PATH = $LOGS_PATH"

        # 创建配置目录
        mkdir -p "$CONFIG_DIR"

        # 创建数据和日志目录（处理逗号分隔的路径）
        IFS=',' read -ra data_paths <<< "$DATA_PATH"
        IFS=',' read -ra logs_paths <<< "$LOGS_PATH"

        for path in "${data_paths[@]}"; do
            mkdir -p "$path"
            rm -rf "$path"/*
        done

        for path in "${logs_paths[@]}"; do
            mkdir -p "$path"
        done
        echo "j = ${j}"
        cat > "$CONFIG_FILE" <<EOF
cluster.name: $CLUSTER_NAME
node.name: node${j}_$matched_ip
network.host: $matched_ip
transport.port: $TCP_PORT
http.port: $HTTP_PORT
path.data: $DATA_PATH
path.logs: $LOGS_PATH
discovery.seed_hosts: [${portListStr}]
cluster.initial_master_nodes: [$nodeListStr]
xpack.security.enabled: false
xpack.security.transport.ssl.enabled: false
xpack.security.http.ssl.enabled: false
http.netty.worker_count: 4
http.cors.enabled: true
http.cors.allow-origin: '*'
EOF

        cp /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/jvm.options "$CONFIG_DIR"
        cp /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/log4j2.properties "$CONFIG_DIR"
        echo "-Xms$MEMORY_SIZE" >> "$CONFIG_DIR/jvm.options"

        cat "$CONFIG_FILE"
    done

    useradd es 2>/dev/null || echo "用户es已存在"

    # 设置所有挂载点的权限
    if [[ ${#data_mount_points[@]} -gt 0 ]]; then
        for mount_point in "${data_mount_points[@]}"; do
            chown -R es:es "$mount_point/es" 2>/dev/null || echo "设置 $mount_point 权限"
        done
    else
        chown -R es:es /data1/es 2>/dev/null || echo "设置默认数据目录权限"
    fi

    chown -R es:es /opt/PostSilicon-Elasticsearch

    start=0
    end=0
    intelstart1=64
    AMDstart=128
    cpu_vendor=$(dmidecode -t processor | grep 'Manufacturer' | uniq)

    for ((a=1; a<=count; a++)); do
        if [[ "$cpu_vendor" =~ "Intel" ]]; then
            end=$((start + 2 * 7))
            CPU=$(seq -s, $start 2 $end)
            start=$((end+2))
            end1=$((intelstart1 + 2 * 7))
            CPU1=$(seq -s, $intelstart1 2 $end1)
            intelstart1=$((end1+2))
            CPU2="$CPU,$CPU1"
            su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU2 /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        elif [[ "$cpu_vendor" =~ "Ampere" ]]; then
            start=$((CORES_PER_INSTANCE * (a - 1)))
            end=$(((CORES_PER_INSTANCE * a) - 1))
            CPU="$start-$end"
            su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        elif [[ "$cpu_vendor" =~ "AMD" ]]; then
            start=$((CORES_PER_INSTANCE/2 * (a - 1)))
            end=$(((CORES_PER_INSTANCE/2 * a) - 1))
            start1=$((AMDstart + CORES_PER_INSTANCE/2 * (a - 1)))
            end1=$(((AMDstart + CORES_PER_INSTANCE/2 * a) - 1))
            CPU="$start-$end,$start1-$end1"
            su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        else
			start=$((CORES_PER_INSTANCE * (a - 1)))
			end=$(((CORES_PER_INSTANCE * a) - 1))
			CPU="$start-$end"
			su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        fi
    done

}

paraller_test(){
	# tqs
	  echo "i = ${i}"
	  for remote_ip in "${remote_hosts[@]}"; do
		   ssh $remote_ip "sysctl -w vm.max_map_count=262144;cp /nas/damon.xu/cluster_test/es/es_cluster_test.sh /opt;export matched_ip=$remote_ip;export instance_count=${i};export CORES_PER_INSTANCE=$CORES_PER_INSTANCE;export MEMORY_SIZE=$MEMORY_SIZE;export disk_devices=$disk_devices;source /opt/es_cluster_test.sh;paraller_test_config_single_marchine"
	done

	sleep 10

}


start_paraller_test(){
	i=0
	echo "ip = $remote_hosts"
	for host in "${remote_hosts[@]}";do
		ssh "$host" "yes | cp /nas/damon.xu/cluster_script/es_env.sh /opt;source /opt/es_env.sh;download_es;ip_get $1;paraller_test"
		i=$((i+1))
	done
}

download_es(){
	cd /opt
	mkdir -p PostSilicon-Elasticsearch
	# 获取当前系统的架构信息
	ARCH=$(uname -m)

	cd PostSilicon-Elasticsearch

	# 如果当前系统是x86架构
	if [ "$ARCH" == "x86_64" ]; then
		echo "当前系统是x86架构"
		if [ ! -f "elasticsearch-8.9.0-linux-x86_64.tar.gz" ]; then
			echo "elasticsearch-8.9.0-linux-x86_64.tar.gz 文件不存在，开始下载..."
			# 执行下载语句
			wget http://************/bigdata/Elasticsearch/elasticsearch-8.9.0-linux-x86_64.tar.gz
			tar -zxvf elasticsearch-8.9.0-linux-x86_64.tar.gz
		else
			echo "elasticsearch-8.9.0-linux-x86_64.tar.gz 文件已存在，无需下载。"
		fi

	# 如果当前系统是ARM架构
	elif [ "$ARCH" == "aarch64" ]; then
		echo "当前系统是ARM架构"
		if [ ! -f "elasticsearch-8.9.0-linux-aarch64.tar.gz" ]; then
			echo "elasticsearch-8.9.0-linux-aarch64.tar.gz 文件不存在，开始下载..."
			# 执行下载语句
			wget http://************/bigdata/Elasticsearch/elasticsearch-8.9.0-linux-aarch64.tar.gz
			tar -zxvf elasticsearch-8.9.0-linux-aarch64.tar.gz
		else
			echo "elasticsearch-8.9.0-linux-aarch64.tar.gz 文件已存在，无需下载。"
		fi
	else
		echo "未知系统架构：$ARCH"
	fi
}


start_test(){

  echo "正在进行ES 线性度测试"

	ip_get $1

	paraller_test $1

	echo "线性度测试结束"

}

# 主函数调用
main() {
    echo "开始ES多实例测试..."
    echo "配置参数:"
    echo "  实例数量: $instance_count"
    echo "  IP地址列表: ${remote_hosts[*]}"
    echo "  CPU核心数: $CORES_PER_INSTANCE"
    echo "  内存大小: $MEMORY_SIZE"
    echo ""
    start_test
}

# 如果有参数则调用主函数
#if [[ ${#remote_hosts[@]} -gt 0 ]]; then
#    main
#fi



